#
#
#
#
#
#
#
#使用时修改wifi 名称和密码
#
#
#
#
#
#
#


# 导入必要的库和模块
import time, os, sys
from media.sensor import *  # 导入摄像头传感器模块，用于图像采集
from media.display import *  # 导入显示模块，用于图像显示
from media.media import *  # 导入媒体模块，用于媒体资源管理
import network, time, socket  # 网络、时间和套接字相关功能
from machine import Pin  # 硬件引脚控制

# 初始化WIFI指示灯（GPIO 52）
WIFI_LED = Pin(52, Pin.OUT)  # 设置GPIO52为输出模式，控制WIFI状态指示灯

# 配置WIFI网络
wlan = network.WLAN(network.STA_IF)  # 创建STA模式WIFI对象
wlan.active(True)  # 激活WIFI接口

# 检查是否已连接WIFI
if not wlan.isconnected():
    print('开始连接WIFI...')

    # 尝试连接WIFI（仅支持2.4G网络）
    # 参数说明：SSID为'2123'，密码为'88888888'
    wlan.connect('2123', '88888888')

# WIFI连接成功处理
if wlan.isconnected():
    # 点亮WIFI指示灯（蓝灯）
    WIFI_LED.value(1)  # 设置高电平，点亮LED

    # 获取本机IP地址和设置服务器端口
    HOST = wlan.ifconfig()[0]  # 获取WIFI分配的IP地址
    PORT = 8080  # 设置服务器监听端口

    # 打印连接信息
    print(f'连接成功,IP:{HOST}')

    # 创建TCP套接字
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)  # 创建IPv4 TCP套接字
    s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)  # 设置套接字选项，允许地址重用
    addr = socket.getaddrinfo(HOST, PORT)[0][-1]  # 解析地址信息
    s.bind(addr)  # 绑定IP和端口
    s.listen(5)  # 开始监听，最大排队连接数为5

    # 打印服务器启动信息
    print(f"服务器启动: {HOST}:{PORT}...")
    print("真实IP:", wlan.ifconfig()[0])  # 再次确认IP地址
    print(f' 连接成功! 立即访问:http://{HOST}:{PORT}')  # 蓝色高亮可点击URL

    # 简单的HTML页面
    def get_html():
        return f"""<!DOCTYPE html>
<html><head><meta charset="UTF-8"><title>K230视频流</title></head>
<body style="background:#333;color:white;font-family:Arial;text-align:center;padding:20px;">
<h1>K230钢丝断裂检测系统</h1>
<p>设备IP: {HOST} | 状态: 在线</p>
<img src="/video_feed" style="max-width:100%;border:2px solid #666;">
<br><button onclick="location.reload()" style="margin:10px;padding:10px;">刷新</button>
</body></html>"""
    

    def get_html_page():
        # 获取真实IP用于显示
        display_ip = wlan.ifconfig()[0]
        if display_ip == '0.0.0.0':
            display_ip = '请检查网络连接'

        # 漂亮的HTML页面
        html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>K230钢丝断裂检测系统</title>
    <style>
        body {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }}
        .main-content {{
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }}
        .video-section {{
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(20px);
        }}
        .video-container {{
            position: relative;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
        }}
        .video-stream {{
            width: 100%;
            height: auto;
            display: block;
        }}
        .control-panel {{
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(20px);
        }}
        .info-card {{
            background: rgba(255,255,255,0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }}
        .info-item {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }}
        .btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px 0;
            width: 100%;
            transition: transform 0.2s;
        }}
        .btn:hover {{
            transform: translateY(-2px);
        }}
        @media (max-width: 768px) {{
            .main-content {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>"智觉探伤"——基于计算机视觉分析与智能磁场测量系统</h1>
            <p>实时视频监控 | 高清画质 | 智能控制</p>
        </div>

        <div class="main-content">
            <div class="video-section">
                <div class="video-container">
                    <img src="/video_feed" class="video-stream" alt="实时视频流">
                </div>
            </div>

            <div class="control-panel">
                <h3>📊 系统信息</h3>

                <div class="info-card">
                    <div class="info-item">
                        <span>设备IP:</span>
                        <span>{display_ip}</span>
                    </div>
                    <div class="info-item">
                        <span>端口:</span>
                        <span>8080</span>
                    </div>
                    <div class="info-item">
                        <span>分辨率:</span>
                        <span>640 × 480</span>
                    </div>
                    <div class="info-item">
                        <span>状态:</span>
                        <span>🟢 在线</span>
                    </div>
                </div>

                <button class="btn" onclick="location.reload()">🔄 刷新页面</button>
                <button class="btn" onclick="captureImage()">📸 截图保存</button>
            </div>
        </div>
    </div>

    <script>
        function captureImage() {{
            const canvas = document.createElement('canvas');
            const img = document.querySelector('.video-stream');
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);

            const link = document.createElement('a');
            link.download = 'camera_' + new Date().getTime() + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }}
    </script>
</body>
</html>"""
        return html_content

    

    try:
        # 初始化摄像头（移到外面，避免重复初始化）
        sensor = Sensor()  # 创建摄像头对象
        sensor.reset()  # 复位摄像头硬件
        sensor.set_framesize(Sensor.VGA)  # 设置图像分辨率为VGA(640x480)
        sensor.set_pixformat(Sensor.RGB565)  # 设置像素格式为RGB565
    
        # 初始化显示系统
        Display.init(Display.VIRT)  # 使用虚拟显示缓冲区
    
        # 初始化媒体资源管理器
        MediaManager.init()  # 分配必要的媒体资源
    
        sensor.run()  # 启动摄像头采集
        clock = time.clock()  # 创建FPS计时器
        
        
        
        # 主服务器循环 - 持续处理客户端连接
        while True:
            # 等待客户端连接
            client, addr = s.accept()  # 接受客户端连接
            print(f'成功连接: {addr[0]}:{addr[1]}')  # 打印客户端地址信息

            try:
                # 接收HTTP请求
                request = client.recv(1024).decode('utf-8')
                print("收到请求:", request.split('\n')[0] if request else "空请求")

                # 检查是否是HTML页面请求
                if 'GET /' in request and 'video_feed' not in request:
                    # 返回HTML页面
                    print("返回HTML页面")
                    html_content = get_html()
                    response = ("HTTP/1.1 200 OK\r\n"
                                "Content-Type: text/html\r\n"
                                "Connection: close\r\n\r\n" + html_content)
                    client.send(response.encode())
                    client.close()
                    print("HTML页面发送完成，等待下一个连接...")

                elif 'video_feed' in request:
                    # 返回视频流（原始代码逻辑）
                    print("开始视频流传输")
                    client.send("HTTP/1.1 200 OK\r\n"
                                "Server: Tao\r\n"
                                "Content-Type: multipart/x-mixed-replace;boundary=Tao\r\n"
                                "Cache-Control: no-cache\r\n"
                                "Pragma: no-cache\r\n\r\n".encode())

                    # 视频流循环：持续捕获和发送图像
                    try:
                        while True:
                            os.exitpoint()  # 检测IDE中断信号
                            clock.tick()  # 更新FPS计时

                            # 图像采集和处理
                            img = sensor.snapshot()  # 捕获一帧图像
                            Display.show_image(img)  # 在虚拟显示上显示图像
                            imge = img.to_jpeg()  # 将图像转换为JPEG格式

                            # 构建HTTP分块传输头
                            header = "--Tao\r\n" \
                                     "Content-Type: image/jpeg\r\n" \
                                     f"Content-Length: {len(imge)}\r\n\r\n"

                            # 发送图像数据
                            client.send(header.encode())  # 发送HTTP头
                            client.send(imge)  # 发送JPEG图像数据

                            print(clock.fps())  # 打印当前帧率
                    except Exception as video_error:
                        print(f"视频流连接断开: {video_error}")
                        client.close()

                else:
                    # 其他请求，返回404
                    print("返回404")
                    response = "HTTP/1.1 404 Not Found\r\nConnection: close\r\n\r\n404 Not Found"
                    client.send(response.encode())
                    client.close()

            except Exception as e:
                print(f"处理连接时出错: {e}")
                try:
                    client.close()
                except:
                    pass

    # 异常处理部分
    except KeyboardInterrupt as e:
        print("用户中断: ", e)  # 用户手动停止程序
    except BaseException as e:
        print(f"发生异常: {e}")  # 其他异常处理
    finally:
        # 资源释放部分
        try:
            if 'sensor' in locals() and isinstance(sensor, Sensor):
                sensor.stop()  # 停止摄像头采集
        except:
            pass
        try:
            Display.deinit()  # 释放显示资源
        except:
            pass
        try:
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)  # 设置退出点允许睡眠
            time.sleep_ms(100)  # 短暂延时
        except:
            pass
        try:
            MediaManager.deinit()  # 释放媒体资源
        except:
            pass
        try:
            s.close()  # 关闭服务器套接字
        except:
            pass

# WIFI连接失败处理
else:
    # LED闪烁3次提示连接失败
    for i in range(3):
        WIFI_LED.value(1)  # LED亮
        time.sleep_ms(300)  # 延时300ms
        WIFI_LED.value(0)  # LED灭
        time.sleep_ms(300)  # 延时300ms

    # 关闭WIFI并提示
    wlan.active(False)  # 禁用WIFI接口
    print('连接失败,尝试重启!')  # 打印错误信息