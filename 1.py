#
#
#
#
#
#
#
#使用时修改wifi 名称和密码
#
#
#
#
#
#
#


# 导入必要的库和模块
import time, os, sys
from media.sensor import *  # 导入摄像头传感器模块，用于图像采集
from media.display import *  # 导入显示模块，用于图像显示
from media.media import *  # 导入媒体模块，用于媒体资源管理
import network, time, socket  # 网络、时间和套接字相关功能
from machine import Pin  # 硬件引脚控制

# 初始化WIFI指示灯（GPIO 52）
WIFI_LED = Pin(52, Pin.OUT)  # 设置GPIO52为输出模式，控制WIFI状态指示灯

# 配置WIFI网络
wlan = network.WLAN(network.STA_IF)  # 创建STA模式WIFI对象
wlan.active(True)  # 激活WIFI接口

# 检查是否已连接WIFI
if not wlan.isconnected():
    print('开始连接WIFI...')

    # 尝试连接WIFI（仅支持2.4G网络）
    # 参数说明：SSID为'XLB 5099'，密码为'88888888'，超时时间为10秒
    wlan.connect('2123', '88888888')

# WIFI连接成功处理
if wlan.isconnected():
    # 点亮WIFI指示灯（蓝灯）
    WIFI_LED.value(1)  # 设置高电平，点亮LED

    # 获取本机IP地址和设置服务器端口
    HOST = wlan.ifconfig()[0]  # 获取WIFI分配的IP地址
    PORT = 8080  # 设置服务器监听端口

    # 打印连接信息
    print(f'连接成功,IP:{HOST}')

    # 创建TCP套接字
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)  # 创建IPv4 TCP套接字
    s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)  # 设置套接字选项，允许地址重用
    addr = socket.getaddrinfo(HOST, PORT)[0][-1]  # 解析地址信息
    s.bind(addr)  # 绑定IP和端口
    s.listen(5)  # 开始监听，最大排队连接数为5

    # 打印服务器启动信息
    print(f"服务器启动: {HOST}:{PORT}...")
    print("真实IP:", wlan.ifconfig()[0])  # 再次确认IP地址
    print(f' 连接成功! 立即访问:http://{HOST}:{PORT}')  # 蓝色高亮可点击URL

    # 读取HTML文件内容
    def read_html_file():
        try:
            with open('1.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
                # 替换设备IP占位符
                html_content = html_content.replace('{device_ip}', HOST)
                return html_content
        except:
            return """<!DOCTYPE html>
<html><head><title>K230视频流</title></head>
<body><h1>K230视频流</h1><img src="/video_feed" style="width:100%;"></body></html>"""

    # 初始化摄像头（移到外面，避免重复初始化）
    sensor = Sensor()  # 创建摄像头对象
    sensor.reset()  # 复位摄像头硬件
    sensor.set_framesize(Sensor.VGA)  # 设置图像分辨率为VGA(640x480)
    sensor.set_pixformat(Sensor.RGB565)  # 设置像素格式为RGB565

    # 初始化显示系统
    Display.init(Display.VIRT)  # 使用虚拟显示缓冲区

    # 初始化媒体资源管理器
    MediaManager.init()  # 分配必要的媒体资源

    sensor.run()  # 启动摄像头采集
    clock = time.clock()  # 创建FPS计时器

    try:
        # 主服务器循环
        while True:
            # 等待客户端连接
            client, addr = s.accept()  # 接受客户端连接
            print(f'成功连接: {addr[0]}:{addr[1]}')  # 打印客户端地址信息

            try:
                # 接收HTTP请求
                request = client.recv(1024).decode('utf-8')
                print("收到请求:", request.split('\n')[0])  # 打印请求的第一行

                # 解析请求路径
                if 'GET /' in request and 'GET /video_feed' not in request:
                    # 返回HTML页面
                    html_content = read_html_file()
                    response = f"HTTP/1.1 200 OK\r\n" \
                              f"Content-Type: text/html; charset=utf-8\r\n" \
                              f"Content-Length: {len(html_content.encode('utf-8'))}\r\n" \
                              f"Connection: close\r\n\r\n" + html_content
                    client.send(response.encode('utf-8'))
                    client.close()

                elif 'GET /video_feed' in request:
                    # 返回视频流
                    client.send("HTTP/1.1 200 OK\r\n"
                                "Server: Tao\r\n"
                                "Content-Type: multipart/x-mixed-replace;boundary=Tao\r\n"
                                "Cache-Control: no-cache\r\n"
                                "Pragma: no-cache\r\n\r\n".encode())

                    # 视频流循环
                    while True:
                        os.exitpoint()  # 检测IDE中断信号
                        clock.tick()  # 更新FPS计时

                        # 图像采集和处理
                        img = sensor.snapshot()  # 捕获一帧图像
                        Display.show_image(img)  # 在虚拟显示上显示图像
                        imge = img.to_jpeg()  # 将图像转换为JPEG格式

                        # 构建HTTP分块传输头
                        header = "--Tao\r\n" \
                                 "Content-Type: image/jpeg\r\n" \
                                 f"Content-Length: {len(imge)}\r\n\r\n"

                        # 发送图像数据
                        client.send(header.encode())  # 发送HTTP头
                        client.send(imge)  # 发送JPEG图像数据

                        print(clock.fps())  # 打印当前帧率
                else:
                    # 404响应
                    response = "HTTP/1.1 404 Not Found\r\nContent-Type: text/html\r\nConnection: close\r\n\r\n<h1>404 Not Found</h1>"
                    client.send(response.encode())
                    client.close()

            except Exception as e:
                print(f"处理请求时出错: {e}")
                try:
                    client.close()
                except:
                    pass

    # 异常处理部分
    except KeyboardInterrupt as e:
        print("用户中断: ", e)  # 用户手动停止程序
    except BaseException as e:
        print(f"发生异常: {e}")  # 其他异常处理
    finally:
        # 资源释放部分
        if isinstance(sensor, Sensor):
            sensor.stop()  # 停止摄像头采集
        Display.deinit()  # 释放显示资源
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)  # 设置退出点允许睡眠
        time.sleep_ms(100)  # 短暂延时
        MediaManager.deinit()  # 释放媒体资源

# WIFI连接失败处理
else:
    # LED闪烁3次提示连接失败
    for i in range(3):
        WIFI_LED.value(1)  # LED亮
        time.sleep_ms(300)  # 延时300ms
        WIFI_LED.value(0)  # LED灭
        time.sleep_ms(300)  # 延时300ms

    # 关闭WIFI并提示
    wlan.active(False)  # 禁用WIFI接口
    print('连接失败,尝试重启!')  # 打印错误信息